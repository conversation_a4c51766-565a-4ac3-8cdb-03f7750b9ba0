'use client'

import { useState, useEffect } from 'react'
import { CertificazioneCavo, CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'
import { certificazioniApi, caviApi, responsabiliApi, cantieriApi, strumentiApi } from '@/lib/api'

interface UseCertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  preselectedCavoId?: string
  onSuccess: (certificazione: CertificazioneCavo) => void
  onCancel: () => void
}

interface WeatherData {
  temperature: number
  humidity: number
  city?: string
  isDemo: boolean
  source: string
}

export function useCertificazioneForm({
  cantiereId,
  certificazione,
  preselectedCavoId,
  onSuccess,
  onCancel
}: UseCertificazioneFormProps) {
  // Stati principali
  const [formData, setFormData] = useState<Partial<CertificazioneCavoCreate>>({
    id_cantiere: cantiereId,
    tipo_certificato: 'SINGOLO',
    stato_certificato: 'BOZZA',
    valore_continuita: 'CONFORME',
    tensione_prova_isolamento: 500,
    temperatura_prova: 20,
    umidita_prova: 50
  })

  // Stati di controllo
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isLoadingWeather, setIsLoadingWeather] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [isWeatherOverride, setIsWeatherOverride] = useState(false)

  // Dati di supporto
  const [cavi, setCavi] = useState<any[]>([])
  const [responsabili, setResponsabili] = useState<any[]>([])
  const [strumenti, setStrumenti] = useState<StrumentoCertificato[]>([])
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)

  // Flags di stato
  const isEdit = !!certificazione
  const isCavoLocked = !!preselectedCavoId

  // Cavo selezionato
  const selectedCavo = formData.id_cavo ? cavi.find(c => c.id_cavo === formData.id_cavo) : null

  // Caricamento dati iniziali
  useEffect(() => {
    loadInitialData()
    loadWeatherData()
  }, [cantiereId])

  // Popolamento form con certificazione esistente
  useEffect(() => {
    if (certificazione) {
      setFormData({
        ...certificazione,
        id_cantiere: cantiereId
      })
    }
  }, [certificazione, cantiereId])

  // Preseleziona cavo se specificato
  useEffect(() => {
    if (preselectedCavoId && !certificazione) {
      setFormData(prev => ({
        ...prev,
        id_cavo: preselectedCavoId
      }))
    }
  }, [preselectedCavoId, certificazione])

  // Integrazione dati meteo per nuove certificazioni
  useEffect(() => {
    if (weatherData && !certificazione && !isWeatherOverride) {
      setFormData(prev => ({
        ...prev,
        temperatura_prova: weatherData.temperature,
        umidita_prova: weatherData.humidity
      }))
    }
  }, [weatherData, certificazione, isWeatherOverride])

  // Funzioni di caricamento dati
  async function loadInitialData() {
    try {
      setIsLoading(true)
      const [caviResponse, responsabiliResponse, strumentiResponse] = await Promise.all([
        caviApi.getCavi(cantiereId),
        responsabiliApi.getResponsabili(cantiereId),
        strumentiApi.getStrumenti(cantiereId)
      ])

      setCavi(caviResponse.data || [])
      setResponsabili(responsabiliResponse.data || [])
      setStrumenti(strumentiResponse.data || [])
    } catch (err) {
      console.error('Errore caricamento dati:', err)
      setError('Errore nel caricamento dei dati iniziali')
    } finally {
      setIsLoading(false)
    }
  }

  // Funzione per ricaricare solo gli strumenti
  async function refreshStrumenti() {
    try {
      const strumentiResponse = await strumentiApi.getStrumenti(cantiereId)
      setStrumenti(strumentiResponse.data || [])
    } catch (err) {
      console.error('Errore ricaricamento strumenti:', err)
    }
  }

  async function loadWeatherData() {
    try {
      setIsLoadingWeather(true)
      const response = await cantieriApi.getWeatherData(cantiereId)
      if (response.data) {
        setWeatherData(response.data)
      }
    } catch (err) {
      console.error('Errore caricamento meteo:', err)
      // Non è un errore critico, continua senza dati meteo
    } finally {
      setIsLoadingWeather(false)
    }
  }

  // Gestione input
  function handleInputChange(field: string, value: any) {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Rimuovi errore di validazione se presente
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // Validazione form
  function validateForm(): boolean {
    const errors: Record<string, string> = {}

    if (!formData.id_cavo) {
      errors.id_cavo = 'Seleziona un cavo'
    }
    if (!formData.valore_isolamento || formData.valore_isolamento <= 0) {
      errors.valore_isolamento = 'Inserisci un valore di isolamento valido'
    }
    if (!formData.valore_continuita) {
      errors.valore_continuita = 'Seleziona il risultato della continuità'
    }
    if (!formData.valore_resistenza) {
      errors.valore_resistenza = 'Inserisci un valore di resistenza'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Preparazione dati per sottomissione
  function prepareSubmissionData(): CertificazioneCavoCreate {
    const submissionData = { ...formData } as CertificazioneCavoCreate

    // Usa dati meteo come fallback se non sovrascritti manualmente
    if (weatherData && !isWeatherOverride) {
      submissionData.temperatura_prova = weatherData.temperature
      submissionData.umidita_prova = weatherData.humidity
    }

    // Assicurati che i campi obbligatori siano presenti
    submissionData.id_cantiere = cantiereId
    submissionData.data_certificazione = submissionData.data_certificazione || new Date().toISOString().split('T')[0]

    return submissionData
  }

  // Sottomissione form
  async function handleSubmit() {
    if (!validateForm()) {
      setError('Correggi gli errori nel form prima di continuare')
      return
    }

    try {
      setIsSaving(true)
      setError('')

      const submissionData = prepareSubmissionData()

      let result
      if (isEdit && certificazione) {
        result = await certificazioniApi.updateCertificazione(cantiereId, certificazione.id_certificazione, submissionData)
      } else {
        result = await certificazioniApi.createCertificazione(cantiereId, submissionData)
      }

      if (result.data) {
        onSuccess(result.data)
      }
    } catch (err: any) {
      console.error('Errore salvataggio:', err)
      setError(err.response?.data?.detail || 'Errore durante il salvataggio')
    } finally {
      setIsSaving(false)
    }
  }

  return {
    // Dati
    formData,
    cavi,
    responsabili,
    strumenti,
    weatherData,
    selectedCavo,

    // Stati
    isLoading,
    isSaving,
    isLoadingWeather,
    error,
    validationErrors,
    isWeatherOverride,
    isEdit,
    isCavoLocked,

    // Funzioni
    handleInputChange,
    handleSubmit,
    setIsWeatherOverride,
    refreshStrumenti,
    onCancel
  }
}
