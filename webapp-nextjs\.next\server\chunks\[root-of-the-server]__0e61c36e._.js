module.exports = {

"[project]/.next-internal/server/app/api/responsabili/cantiere/[cantiereId]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/responsabili/cantiere/[cantiereId]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET(request, { params }) {
    try {
        const cantiereId = parseInt(params.cantiereId);
        if (isNaN(cantiereId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'ID cantiere non valido'
            }, {
                status: 400
            });
        }
        // Per ora restituiamo dati mock per testare l'interfaccia
        // TODO: Implementare chiamata al backend Python
        const mockResponsabili = [
            {
                id_responsabile: 1,
                nome_responsabile: "Mario Rossi",
                telefono: "+39 ************",
                email: "<EMAIL>",
                experience_level: "Senior",
                id_cantiere: cantiereId,
                data_creazione: "2024-01-15T10:00:00Z",
                attivo: true
            },
            {
                id_responsabile: 2,
                nome_responsabile: "Luigi Verdi",
                telefono: "+39 ************",
                email: "<EMAIL>",
                experience_level: "Senior",
                id_cantiere: cantiereId,
                data_creazione: "2024-01-20T14:30:00Z",
                attivo: true
            },
            {
                id_responsabile: 3,
                nome_responsabile: "Anna Bianchi",
                telefono: "+39 ************",
                email: "<EMAIL>",
                experience_level: "Junior",
                id_cantiere: cantiereId,
                data_creazione: "2024-02-01T09:15:00Z",
                attivo: true
            }
        ];
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: mockResponsabili,
            total: mockResponsabili.length
        });
    } catch (error) {
        console.error('Errore nel recupero responsabili:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Errore interno del server'
        }, {
            status: 500
        });
    }
}
async function POST(request, { params }) {
    try {
        const cantiereId = parseInt(params.cantiereId);
        const body = await request.json();
        if (isNaN(cantiereId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'ID cantiere non valido'
            }, {
                status: 400
            });
        }
        // Validazione base
        if (!body.nome_responsabile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Nome responsabile obbligatorio'
            }, {
                status: 400
            });
        }
        // Per ora restituiamo un mock del responsabile creato
        // TODO: Implementare chiamata al backend Python
        const nuovoResponsabile = {
            id_responsabile: Math.floor(Math.random() * 1000) + 100,
            nome_responsabile: body.nome_responsabile,
            telefono: body.telefono || null,
            email: body.email || null,
            experience_level: body.experience_level || 'Senior',
            id_cantiere: cantiereId,
            data_creazione: new Date().toISOString(),
            attivo: true
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: nuovoResponsabile
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Errore nella creazione responsabile:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Errore interno del server'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0e61c36e._.js.map