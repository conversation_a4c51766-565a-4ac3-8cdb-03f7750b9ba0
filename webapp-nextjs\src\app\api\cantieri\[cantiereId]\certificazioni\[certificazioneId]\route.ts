import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8001'

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string; certificazioneId: string } }
) {
  try {
    console.log('🔄 Certificazione API: Proxying GET request to backend:', `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni/${params.certificazioneId}`)
    
    // Ottieni il token dall'header Authorization
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })
    }

    const response = await fetch(`${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni/${params.certificazioneId}`, {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    })

    console.log('📡 Certificazione API: Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Certificazione API: Backend error:', errorText)
      return NextResponse.json(
        { error: 'Errore dal backend', details: errorText },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('📡 Certificazione API: Backend response data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('❌ Certificazione API: Error:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { cantiereId: string; certificazioneId: string } }
) {
  try {
    console.log('🔄 Certificazione API: Proxying PUT request to backend:', `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni/${params.certificazioneId}`)
    
    // Ottieni il token dall'header Authorization
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })
    }

    // Ottieni il body della richiesta
    const body = await request.json()
    console.log('📡 Certificazione API: Request body:', body)

    const response = await fetch(`${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni/${params.certificazioneId}`, {
      method: 'PUT',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log('📡 Certificazione API: Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Certificazione API: Backend error:', errorText)
      return NextResponse.json(
        { error: 'Errore dal backend', details: errorText },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('📡 Certificazione API: Backend response data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('❌ Certificazione API: Error:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { cantiereId: string; certificazioneId: string } }
) {
  try {
    console.log('🔄 Certificazione API: Proxying DELETE request to backend:', `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni/${params.certificazioneId}`)
    
    // Ottieni il token dall'header Authorization
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })
    }

    const response = await fetch(`${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni/${params.certificazioneId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    })

    console.log('📡 Certificazione API: Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Certificazione API: Backend error:', errorText)
      return NextResponse.json(
        { error: 'Errore dal backend', details: errorText },
        { status: response.status }
      )
    }

    // DELETE potrebbe non restituire contenuto
    if (response.status === 204) {
      return new NextResponse(null, { status: 204 })
    }

    const data = await response.json()
    console.log('📡 Certificazione API: Backend response data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('❌ Certificazione API: Error:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
