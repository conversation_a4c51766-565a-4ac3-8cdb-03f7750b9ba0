import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Estrai i dati del form
    const formData = await request.formData()
    const username = formData.get('username') as string
    const password = formData.get('password') as string

    if (!username || !password) {
      return NextResponse.json(
        { 
          detail: 'Username e password sono richiesti' 
        }, 
        { status: 400 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    // Crea FormData per il backend
    const backendFormData = new FormData()
    backendFormData.append('username', username)
    backendFormData.append('password', password)

    console.log('🔄 Auth API: Proxying login request to backend:', `${backendUrl}/api/auth/login`)
    
    const response = await fetch(`${backendUrl}/api/auth/login`, {
      method: 'POST',
      body: backendFormData
    })

    console.log('📡 Auth API: Backend response status:', response.status)

    const data = await response.json()
    console.log('📡 Auth API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Auth API: Login error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
