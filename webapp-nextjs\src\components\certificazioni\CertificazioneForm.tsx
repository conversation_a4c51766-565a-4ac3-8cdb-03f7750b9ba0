'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { FileText, Save, Loader2, AlertCircle, X, Settings, Plus } from 'lucide-react'
import { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'
import { useCertificazioneForm } from '@/hooks/useCertificazioneForm'
import StrumentoForm from './StrumentoForm'

interface CertificazioneFormProps {
  cantiereId: number
  certificazione?: CertificazioneCavo | null
  strumenti: StrumentoCertificato[]
  preselectedCavoId?: string
  onSuccess: (certificazione: CertificazioneCavo) => void
  onCancel: () => void
  onStrumentiUpdate?: () => void
}

/**
 * Form di certificazione CEI 64-8 completamente ridisegnato
 * Layout moderno a singola colonna con sezioni organizzate
 */
export default function CertificazioneForm({
  cantiereId,
  certificazione,
  strumenti,
  preselectedCavoId,
  onSuccess,
  onCancel,
  onStrumentiUpdate
}: CertificazioneFormProps) {
  // Stato per il modal del nuovo strumento
  const [showStrumentoForm, setShowStrumentoForm] = useState(false)
  const {
    // Dati
    formData,
    cavi,
    responsabili,
    strumenti: strumentiFromHook,
    weatherData,
    selectedCavo,

    // Stati
    isLoading,
    isSaving,
    isLoadingWeather,
    error,
    validationErrors,
    isWeatherOverride,
    isEdit,
    isCavoLocked,

    // Funzioni
    handleInputChange,
    handleSubmit,
    setIsWeatherOverride,
    refreshStrumenti,
    onCancel: handleCancel
  } = useCertificazioneForm({
    cantiereId,
    certificazione,
    strumenti,
    preselectedCavoId,
    onSuccess,
    onCancel
  })

  // Usa gli strumenti dal hook se disponibili, altrimenti quelli passati come prop
  const strumentiToUse = strumentiFromHook.length > 0 ? strumentiFromHook : strumenti

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Caricamento dati certificazione...</p>
        </div>
      </div>
    )
  }

  // Render del componente principale
  return (
    <div className="min-w-[800px] max-w-4xl mx-auto bg-white">
      {/* Header Compatto */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-blue-100 rounded-lg">
            <FileText className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}
            </h1>
            {preselectedCavoId && (
              <p className="text-xs text-gray-500">Cavo: {preselectedCavoId}</p>
            )}
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600 h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Alert Errori */}
      {error && (
        <div className="p-4 border-b border-red-200 bg-red-50">
          <Alert variant="destructive" className="border-red-200 bg-red-50 py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-sm">{error}</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Form Compatto */}
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="p-6 space-y-6">

        {/* Sezione 1: Informazioni Base */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <FileText className="h-4 w-4 text-blue-600" />
            <h2 className="text-base font-semibold text-gray-900">Informazioni Base</h2>
            <span className="text-xs text-gray-500">Dati principali del cavo e operatore</span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Informazioni Cavo Selezionato */}
            {selectedCavo && (
              <div className="md:col-span-2 p-4 bg-gray-50 rounded-lg border">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Cavo: {selectedCavo.id_cavo}</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div><span className="font-medium text-gray-600">Tipologia:</span> <span className="font-bold">{selectedCavo.tipologia || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Sezione:</span> <span className="font-bold">{selectedCavo.sezione || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Sistema:</span> <span className="font-bold">{selectedCavo.sistema || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Utility:</span> <span className="font-bold">{selectedCavo.utility || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Da:</span> <span className="font-bold">{selectedCavo.ubicazione_partenza || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">A:</span> <span className="font-bold">{selectedCavo.ubicazione_arrivo || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Metri Teorici:</span> <span className="font-bold">{selectedCavo.metri_teorici || 0}</span></div>
                  <div><span className="font-medium text-gray-600">Metri Reali:</span> <span className="font-bold">{selectedCavo.metratura_reale || 0}</span></div>
                  <div><span className="font-medium text-gray-600">Stato:</span> <span className="font-bold">{selectedCavo.stato_installazione || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Bobina:</span> <span className="font-bold">{selectedCavo.id_bobina || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Colore:</span> <span className="font-bold">{selectedCavo.colore_cavo || 'N/A'}</span></div>
                  <div><span className="font-medium text-gray-600">Collegamenti:</span> <span className="font-bold">{selectedCavo.collegamenti || 0}</span></div>
                </div>
              </div>
            )}

            {/* Operatore */}
            <div className="space-y-1">
              <Label htmlFor="id_operatore" className="text-xs font-medium text-gray-700">Operatore</Label>
              <Select
                value={formData.id_operatore || ''}
                onValueChange={(value) => handleInputChange('id_operatore', value)}
              >
                <SelectTrigger className="h-9 text-sm border-gray-300">
                  <SelectValue placeholder="Seleziona operatore..." />
                </SelectTrigger>
                <SelectContent>
                  {responsabili.map((resp) => (
                    <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>
                      <span className="text-sm">{resp.nome_responsabile}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Strumento */}
            <div className="space-y-1">
              <Label htmlFor="id_strumento" className="text-xs font-medium text-gray-700">Strumento di Misura</Label>
              <div className="flex gap-2">
                <Select
                  value={formData.id_strumento?.toString() || ''}
                  onValueChange={(value) => {
                    const strumento = strumentiToUse.find(s => s.id_strumento === parseInt(value))
                    handleInputChange('id_strumento', parseInt(value))
                    if (strumento) {
                      handleInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)
                    }
                  }}
                >
                  <SelectTrigger className="h-9 text-sm border-gray-300 flex-1">
                    <SelectValue placeholder="Seleziona strumento..." />
                  </SelectTrigger>
                  <SelectContent>
                    {strumentiToUse.map((strumento) => (
                      <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>
                        <span className="font-medium text-sm">{strumento.nome}</span>
                        <span className="text-xs text-gray-500 ml-2">{strumento.marca} {strumento.modello}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="h-9 w-9 p-0 shrink-0"
                  onClick={() => setShowStrumentoForm(true)}
                  title="Aggiungi nuovo strumento"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>


          </div>
        </div>

        {/* Sezione 2: Condizioni Ambientali */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <span className="text-lg">🌤️</span>
            <h2 className="text-base font-semibold text-gray-900">Condizioni Ambientali</h2>
            <span className="text-xs text-gray-500">Temperatura e umidità durante la certificazione</span>
          </div>

          {/* Dati Meteorologici Compatti */}
          {weatherData && (
            <div className={`p-3 rounded-lg border ${
              weatherData.isDemo
                ? 'bg-amber-50 border-amber-200'
                : 'bg-emerald-50 border-emerald-200'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isLoadingWeather ? (
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  ) : (
                    <span className="text-lg">{weatherData.isDemo ? '🔧' : '🌤️'}</span>
                  )}
                  <div>
                    <div className="text-sm font-semibold text-gray-900">
                      {weatherData.temperature}°C • {weatherData.humidity}% UR
                    </div>
                    {weatherData.city && (
                      <div className="text-xs text-gray-600">{weatherData.city}</div>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    📡 {weatherData.source}
                  </div>
                </div>

                <Button
                  type="button"
                  variant={isWeatherOverride ? "default" : "outline"}
                  size="sm"
                  onClick={() => setIsWeatherOverride(!isWeatherOverride)}
                  className="h-7 text-xs"
                >
                  {isWeatherOverride ? (
                    <>
                      <X className="h-3 w-3 mr-1" />
                      Automatico
                    </>
                  ) : (
                    <>
                      <Settings className="h-3 w-3 mr-1" />
                      Manuale
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {/* Override Manuale Compatto */}
          {isWeatherOverride && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="space-y-1">
                <Label htmlFor="temperatura_prova" className="text-xs font-medium text-gray-700">
                  Temperatura (°C)
                </Label>
                <Input
                  id="temperatura_prova"
                  type="number"
                  step="0.1"
                  value={formData.temperatura_prova || ''}
                  onChange={(e) => handleInputChange('temperatura_prova', parseFloat(e.target.value))}
                  placeholder="20.0"
                  className="h-8 text-sm"
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="umidita_prova" className="text-xs font-medium text-gray-700">
                  Umidità Relativa (%)
                </Label>
                <Input
                  id="umidita_prova"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.umidita_prova || ''}
                  onChange={(e) => handleInputChange('umidita_prova', parseFloat(e.target.value))}
                  placeholder="50"
                  className="h-8 text-sm"
                />
              </div>
            </div>
          )}
        </div>

        {/* Sezione 3: Misurazioni e Test Compatte */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <span className="text-lg">⚡</span>
            <h2 className="text-base font-semibold text-gray-900">Misurazioni e Test CEI 64-8</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {/* Continuità */}
            <div className="space-y-1">
              <Label htmlFor="valore_continuita" className="text-xs font-medium text-gray-700">
                Continuità
              </Label>
              <Select
                value={formData.valore_continuita || 'OK'}
                onValueChange={(value) => handleInputChange('valore_continuita', value)}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="OK">✅ OK</SelectItem>
                  <SelectItem value="NON_OK">❌ NON OK</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Isolamento */}
            <div className="space-y-1">
              <Label htmlFor="valore_isolamento" className="text-xs font-medium text-gray-700">
                Isolamento (MΩ)
              </Label>
              <Input
                id="valore_isolamento"
                value={formData.valore_isolamento || ''}
                onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}
                placeholder="500"
                className="h-8 text-sm"
              />
            </div>

            {/* Resistenza */}
            <div className="space-y-1">
              <Label htmlFor="valore_resistenza" className="text-xs font-medium text-gray-700">
                Resistenza (Ω)
              </Label>
              <Input
                id="valore_resistenza"
                value={formData.valore_resistenza || ''}
                onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}
                placeholder="0.5"
                className="h-8 text-sm"
              />
            </div>



            {/* Tensione Prova */}
            <div className="space-y-1">
              <Label htmlFor="tensione_prova_isolamento" className="text-xs font-medium text-gray-700">
                Tensione Prova (V)
              </Label>
              <Input
                id="tensione_prova_isolamento"
                type="number"
                value={formData.tensione_prova_isolamento || ''}
                onChange={(e) => handleInputChange('tensione_prova_isolamento', parseInt(e.target.value))}
                placeholder="500"
                className="h-8 text-sm"
              />
            </div>

            {/* Esito */}
            <div className="space-y-1">
              <Label htmlFor="esito_complessivo" className="text-xs font-medium text-gray-700">
                Esito Complessivo
              </Label>
              <Select
                value={formData.esito_complessivo || 'CONFORME'}
                onValueChange={(value) => handleInputChange('esito_complessivo', value)}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CONFORME">✅ CONFORME</SelectItem>
                  <SelectItem value="NON_CONFORME">❌ NON CONFORME</SelectItem>
                  <SelectItem value="CONFORME_CON_OSSERVAZIONI">⚠️ CONFORME CON OSSERVAZIONI</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Note Compatte */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
            <span className="text-lg">📝</span>
            <h2 className="text-base font-semibold text-gray-900">Note</h2>
          </div>

          <div className="space-y-1">
            <Label htmlFor="note" className="text-xs font-medium text-gray-700">
              Osservazioni aggiuntive
            </Label>
            <Textarea
              id="note"
              value={formData.note || ''}
              onChange={(e) => handleInputChange('note', e.target.value)}
              placeholder="Inserisci eventuali note o osservazioni..."
              className="h-16 text-sm resize-none"
            />
          </div>
        </div>

        {/* Footer Azioni */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSaving}
            className="px-4 h-9"
          >
            Annulla
          </Button>
          <Button
            type="submit"
            disabled={isSaving}
            className="px-6 h-9 bg-green-600 hover:bg-green-700"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Salvando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Salva e Chiudi
              </>
            )}
          </Button>
        </div>
      </form>

      {/* Modal Nuovo Strumento */}
      <Dialog open={showStrumentoForm} onOpenChange={setShowStrumentoForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Aggiungi Nuovo Strumento</DialogTitle>
          </DialogHeader>
          <StrumentoForm
            cantiereId={cantiereId}
            onSuccess={() => {
              setShowStrumentoForm(false)
              // Ricarica la lista degli strumenti
              refreshStrumenti()
              if (onStrumentiUpdate) {
                onStrumentiUpdate()
              }
            }}
            onCancel={() => setShowStrumentoForm(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
