{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/cantieri/%5BcantiereId%5D/certificazioni/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nconst BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    console.log('🔄 Certificazioni API: Proxying GET request to backend:', `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni`)\n    \n    // Ottieni il token dall'header Authorization\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })\n    }\n\n    // Ottieni i parametri di query\n    const { searchParams } = new URL(request.url)\n    const queryString = searchParams.toString()\n    const backendUrl = `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni${queryString ? `?${queryString}` : ''}`\n\n    const response = await fetch(backendUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': authHeader,\n        'Content-Type': 'application/json',\n      },\n    })\n\n    console.log('📡 Certificazioni API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error('❌ Certificazioni API: Backend error:', errorText)\n      return NextResponse.json(\n        { error: 'Errore dal backend', details: errorText },\n        { status: response.status }\n      )\n    }\n\n    const data = await response.json()\n    console.log('📡 Certificazioni API: Backend response data:', data)\n\n    return NextResponse.json(data)\n  } catch (error) {\n    console.error('❌ Certificazioni API: Error:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    console.log('🔄 Certificazioni API: Proxying POST request to backend:', `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni`)\n    \n    // Ottieni il token dall'header Authorization\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })\n    }\n\n    // Ottieni il body della richiesta\n    const body = await request.json()\n    console.log('📡 Certificazioni API: Request body:', JSON.stringify(body, null, 2))\n    console.log('📡 Certificazioni API: Sending to backend URL:', `${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni`)\n\n    const response = await fetch(`${BACKEND_URL}/api/cantieri/${params.cantiereId}/certificazioni`, {\n      method: 'POST',\n      headers: {\n        'Authorization': authHeader,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(body),\n    })\n\n    console.log('📡 Certificazioni API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorText = await response.text()\n      console.error('❌ Certificazioni API: Backend error:', errorText)\n      return NextResponse.json(\n        { error: 'Errore dal backend', details: errorText },\n        { status: response.status }\n      )\n    }\n\n    const data = await response.json()\n    console.log('📡 Certificazioni API: Backend response data:', data)\n\n    return NextResponse.json(data)\n  } catch (error) {\n    console.error('❌ Certificazioni API: Error:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAExC,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,QAAQ,GAAG,CAAC,2DAA2D,GAAG,YAAY,cAAc,EAAE,OAAO,UAAU,CAAC,eAAe,CAAC;QAExI,6CAA6C;QAC7C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmC,GAAG;gBAAE,QAAQ;YAAI;QACxF;QAEA,+BAA+B;QAC/B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,aAAa,GAAG,YAAY,cAAc,EAAE,OAAO,UAAU,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAE3H,MAAM,WAAW,MAAM,MAAM,YAAY;YACvC,QAAQ;YACR,SAAS;gBACP,iBAAiB;gBACjB,gBAAgB;YAClB;QACF;QAEA,QAAQ,GAAG,CAAC,mDAAmD,SAAS,MAAM;QAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAsB,SAAS;YAAU,GAClD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,iDAAiD;QAE7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAA6B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAgB,GACxG;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,QAAQ,GAAG,CAAC,4DAA4D,GAAG,YAAY,cAAc,EAAE,OAAO,UAAU,CAAC,eAAe,CAAC;QAEzI,6CAA6C;QAC7C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmC,GAAG;gBAAE,QAAQ;YAAI;QACxF;QAEA,kCAAkC;QAClC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,QAAQ,GAAG,CAAC,wCAAwC,KAAK,SAAS,CAAC,MAAM,MAAM;QAC/E,QAAQ,GAAG,CAAC,kDAAkD,GAAG,YAAY,cAAc,EAAE,OAAO,UAAU,CAAC,eAAe,CAAC;QAE/H,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,cAAc,EAAE,OAAO,UAAU,CAAC,eAAe,CAAC,EAAE;YAC9F,QAAQ;YACR,SAAS;gBACP,iBAAiB;gBACjB,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,QAAQ,GAAG,CAAC,mDAAmD,SAAS,MAAM;QAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAsB,SAAS;YAAU,GAClD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,iDAAiD;QAE7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAA6B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAgB,GACxG;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}