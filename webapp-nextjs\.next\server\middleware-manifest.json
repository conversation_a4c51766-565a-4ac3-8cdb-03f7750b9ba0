{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "782b33a7443ae59f30f65f4c913552a3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "912d07d32ab417e92f093410aee69217808cddd580298082d77c086bd380a10e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f666502870b79fd31928f30225cec426e627ad8f0da583cd84667d7b133da0ef"}}}, "sortedMiddleware": ["/"], "functions": {}}