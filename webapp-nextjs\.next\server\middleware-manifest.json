{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "1095c81c96d96c134bd8b58847a63464", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d44e713705e7d325ad582647be4962033fe26635bfd72b31eb3a35a303b8f9a6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4a7b0e05b30e217f34b9d9acdeaddc7da7a594668744b7bfed28da06cecfd374"}}}, "sortedMiddleware": ["/"], "functions": {}}