{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "8dc2946146991d850369dbc947772665", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "21641eda116cbf9c3330c4282a247b3126a98e9b937c2a72bd28dc238316ddd9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f763d0a4c90e598b0bcc2a2b6915b5992a5b3c033359512e8bca8f24ede9be4"}}}, "sortedMiddleware": ["/"], "functions": {}}