{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/cantieri/%5BcantiereId%5D/weather/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  pressure?: number\n  description?: string\n  city?: string\n  country?: string\n  timestamp?: string\n  isDemo?: boolean\n  source?: string\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { cantiereId: string } }\n) {\n  try {\n    const cantiereId = parseInt(params.cantiereId)\n    \n    if (isNaN(cantiereId)) {\n      return NextResponse.json(\n        { error: 'ID cantiere non valido' },\n        { status: 400 }\n      )\n    }\n\n    // Per ora restituiamo dati meteo mock per testare l'interfaccia\n    // TODO: Implementare chiamata al backend Python per dati meteo reali\n    const mockWeatherData: WeatherData = {\n      temperature: 18.5,\n      humidity: 68,\n      pressure: 1013.2,\n      description: \"Partly cloudy\",\n      city: \"Milano\",\n      country: \"IT\",\n      timestamp: new Date().toISOString(),\n      isDemo: true,\n      source: \"cantiere_database\"\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: mockWeatherData,\n      message: \"Dati meteorologici recuperati con successo\"\n    })\n\n  } catch (error) {\n    console.error('Errore nel recupero dati meteo:', error)\n    return NextResponse.json(\n      { error: 'Errore interno del server' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAcO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAsC;IAE9C,IAAI;QACF,MAAM,aAAa,SAAS,OAAO,UAAU;QAE7C,IAAI,MAAM,aAAa;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gEAAgE;QAChE,qEAAqE;QACrE,MAAM,kBAA+B;YACnC,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;YACb,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;YACR,QAAQ;QACV;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}