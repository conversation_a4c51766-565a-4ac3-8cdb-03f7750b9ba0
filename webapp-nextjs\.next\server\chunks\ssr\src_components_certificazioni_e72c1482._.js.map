{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/StrumentoForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Save, X, Settings } from 'lucide-react'\nimport { StrumentoCertificato, StrumentoCertificatoCreate } from '@/types/certificazioni'\nimport { strumentiApi } from '@/lib/api'\n\ninterface StrumentoFormProps {\n  cantiereId: number\n  strumento?: StrumentoCertificato | null\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nexport default function StrumentoForm({\n  cantiereId,\n  strumento,\n  onSuccess,\n  onCancel\n}: StrumentoFormProps) {\n  const [formData, setFormData] = useState<StrumentoCertificatoCreate>({\n    nome: '',\n    marca: '',\n    modello: '',\n    numero_serie: '',\n    data_calibrazione: '',\n    data_scadenza_calibrazione: '',\n    note: '',\n    tipo_strumento: 'MEGGER',\n    ente_certificatore: '',\n    numero_certificato_calibrazione: '',\n    range_misura: '',\n    precisione: '',\n    stato_strumento: 'ATTIVO'\n  })\n\n  const [isSaving, setIsSaving] = useState(false)\n  const [error, setError] = useState('')\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  const isEdit = !!strumento\n\n  useEffect(() => {\n    if (strumento) {\n      setFormData({\n        nome: strumento.nome,\n        marca: strumento.marca,\n        modello: strumento.modello,\n        numero_serie: strumento.numero_serie,\n        data_calibrazione: strumento.data_calibrazione.split('T')[0], // Format for input[type=\"date\"]\n        data_scadenza_calibrazione: strumento.data_scadenza_calibrazione.split('T')[0],\n        note: strumento.note || '',\n        tipo_strumento: strumento.tipo_strumento || 'MEGGER',\n        ente_certificatore: strumento.ente_certificatore || '',\n        numero_certificato_calibrazione: strumento.numero_certificato_calibrazione || '',\n        range_misura: strumento.range_misura || '',\n        precisione: strumento.precisione || '',\n        stato_strumento: strumento.stato_strumento || 'ATTIVO'\n      })\n    }\n  }, [strumento])\n\n  const validateForm = (): boolean => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.nome.trim()) {\n      errors.nome = 'Il nome è obbligatorio'\n    }\n\n    if (!formData.marca.trim()) {\n      errors.marca = 'La marca è obbligatoria'\n    }\n\n    if (!formData.modello.trim()) {\n      errors.modello = 'Il modello è obbligatorio'\n    }\n\n    if (!formData.numero_serie.trim()) {\n      errors.numero_serie = 'Il numero di serie è obbligatorio'\n    }\n\n    if (!formData.data_calibrazione) {\n      errors.data_calibrazione = 'La data di calibrazione è obbligatoria'\n    }\n\n    if (!formData.data_scadenza_calibrazione) {\n      errors.data_scadenza_calibrazione = 'La data di scadenza è obbligatoria'\n    }\n\n    if (formData.data_calibrazione && formData.data_scadenza_calibrazione) {\n      const calibrazione = new Date(formData.data_calibrazione)\n      const scadenza = new Date(formData.data_scadenza_calibrazione)\n      \n      if (scadenza <= calibrazione) {\n        errors.data_scadenza_calibrazione = 'La data di scadenza deve essere successiva alla calibrazione'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    try {\n      setIsSaving(true)\n      setError('')\n\n      if (isEdit && strumento) {\n        await strumentiApi.updateStrumento(cantiereId, strumento.id_strumento, formData)\n      } else {\n        await strumentiApi.createStrumento(cantiereId, formData)\n      }\n\n      onSuccess()\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il salvataggio')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const handleInputChange = (field: keyof StrumentoCertificatoCreate, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    \n    // Rimuovi errore di validazione se presente\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  // Calcola automaticamente la data di scadenza (1 anno dalla calibrazione)\n  const handleCalibrazioneChange = (value: string) => {\n    handleInputChange('data_calibrazione', value)\n    \n    if (value && !formData.data_scadenza_calibrazione) {\n      const calibrazione = new Date(value)\n      const scadenza = new Date(calibrazione)\n      scadenza.setFullYear(scadenza.getFullYear() + 1)\n      handleInputChange('data_scadenza_calibrazione', scadenza.toISOString().split('T')[0])\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-slate-900 flex items-center gap-3\">\n            <Settings className=\"h-6 w-6 text-blue-600\" />\n            {isEdit ? 'Modifica Strumento' : 'Nuovo Strumento'}\n          </h2>\n          <p className=\"text-slate-600 mt-1\">\n            {isEdit ? 'Modifica i dati dello strumento esistente' : 'Aggiungi un nuovo strumento di misura'}\n          </p>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Informazioni Base */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Informazioni Base</CardTitle>\n            <CardDescription>Dati identificativi dello strumento</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"nome\">Nome Strumento *</Label>\n                <Input\n                  id=\"nome\"\n                  value={formData.nome}\n                  onChange={(e) => handleInputChange('nome', e.target.value)}\n                  className={validationErrors.nome ? 'border-red-500' : ''}\n                  placeholder=\"es. Megger MFT1741\"\n                />\n                {validationErrors.nome && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.nome}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"tipo_strumento\">Tipo Strumento</Label>\n                <Select\n                  value={formData.tipo_strumento}\n                  onValueChange={(value) => handleInputChange('tipo_strumento', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"MEGGER\">Megger</SelectItem>\n                    <SelectItem value=\"MULTIMETRO\">Multimetro</SelectItem>\n                    <SelectItem value=\"OSCILLOSCOPIO\">Oscilloscopio</SelectItem>\n                    <SelectItem value=\"ALTRO\">Altro</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"marca\">Marca *</Label>\n                <Input\n                  id=\"marca\"\n                  value={formData.marca}\n                  onChange={(e) => handleInputChange('marca', e.target.value)}\n                  className={validationErrors.marca ? 'border-red-500' : ''}\n                  placeholder=\"es. Fluke, Megger, Keysight\"\n                />\n                {validationErrors.marca && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.marca}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"modello\">Modello *</Label>\n                <Input\n                  id=\"modello\"\n                  value={formData.modello}\n                  onChange={(e) => handleInputChange('modello', e.target.value)}\n                  className={validationErrors.modello ? 'border-red-500' : ''}\n                  placeholder=\"es. MFT1741, 87V\"\n                />\n                {validationErrors.modello && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.modello}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"numero_serie\">Numero Serie *</Label>\n                <Input\n                  id=\"numero_serie\"\n                  value={formData.numero_serie}\n                  onChange={(e) => handleInputChange('numero_serie', e.target.value)}\n                  className={validationErrors.numero_serie ? 'border-red-500' : ''}\n                  placeholder=\"Numero di serie univoco\"\n                />\n                {validationErrors.numero_serie && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.numero_serie}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"stato_strumento\">Stato</Label>\n                <Select\n                  value={formData.stato_strumento}\n                  onValueChange={(value) => handleInputChange('stato_strumento', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"ATTIVO\">Attivo</SelectItem>\n                    <SelectItem value=\"SCADUTO\">Scaduto</SelectItem>\n                    <SelectItem value=\"FUORI_SERVIZIO\">Fuori Servizio</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Calibrazione */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Calibrazione</CardTitle>\n            <CardDescription>Informazioni sulla calibrazione dello strumento</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"data_calibrazione\">Data Calibrazione *</Label>\n                <Input\n                  id=\"data_calibrazione\"\n                  type=\"date\"\n                  value={formData.data_calibrazione}\n                  onChange={(e) => handleCalibrazioneChange(e.target.value)}\n                  className={validationErrors.data_calibrazione ? 'border-red-500' : ''}\n                />\n                {validationErrors.data_calibrazione && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.data_calibrazione}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"data_scadenza_calibrazione\">Data Scadenza *</Label>\n                <Input\n                  id=\"data_scadenza_calibrazione\"\n                  type=\"date\"\n                  value={formData.data_scadenza_calibrazione}\n                  onChange={(e) => handleInputChange('data_scadenza_calibrazione', e.target.value)}\n                  className={validationErrors.data_scadenza_calibrazione ? 'border-red-500' : ''}\n                />\n                {validationErrors.data_scadenza_calibrazione && (\n                  <p className=\"text-sm text-red-600\">{validationErrors.data_scadenza_calibrazione}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ente_certificatore\">Ente Certificatore</Label>\n                <Input\n                  id=\"ente_certificatore\"\n                  value={formData.ente_certificatore}\n                  onChange={(e) => handleInputChange('ente_certificatore', e.target.value)}\n                  placeholder=\"es. LAT 123, ACCREDIA\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"numero_certificato_calibrazione\">Numero Certificato</Label>\n                <Input\n                  id=\"numero_certificato_calibrazione\"\n                  value={formData.numero_certificato_calibrazione}\n                  onChange={(e) => handleInputChange('numero_certificato_calibrazione', e.target.value)}\n                  placeholder=\"Numero del certificato di calibrazione\"\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Specifiche Tecniche */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Specifiche Tecniche</CardTitle>\n            <CardDescription>Caratteristiche tecniche dello strumento</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"range_misura\">Range di Misura</Label>\n                <Input\n                  id=\"range_misura\"\n                  value={formData.range_misura}\n                  onChange={(e) => handleInputChange('range_misura', e.target.value)}\n                  placeholder=\"es. 0-1000V, 0-20A\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"precisione\">Precisione</Label>\n                <Input\n                  id=\"precisione\"\n                  value={formData.precisione}\n                  onChange={(e) => handleInputChange('precisione', e.target.value)}\n                  placeholder=\"es. ±0.1%, ±2 digit\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"note\">Note</Label>\n              <Textarea\n                id=\"note\"\n                value={formData.note}\n                onChange={(e) => handleInputChange('note', e.target.value)}\n                placeholder=\"Note aggiuntive sullo strumento...\"\n                rows={3}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {error && (\n          <Alert className=\"border-red-200 bg-red-50\">\n            <AlertCircle className=\"h-4 w-4 text-red-600\" />\n            <AlertDescription className=\"text-red-800\">{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {/* Pulsanti di azione */}\n        <div className=\"flex gap-3 justify-end pt-4 border-t border-gray-200\">\n          <Button variant=\"outline\" onClick={onCancel} disabled={isSaving}>\n            <X className=\"h-4 w-4 mr-2\" />\n            Annulla\n          </Button>\n          <Button onClick={handleSubmit} disabled={isSaving}>\n            {isSaving ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Save className=\"h-4 w-4 mr-2\" />}\n            {isEdit ? 'Aggiorna' : 'Salva'}\n          </Button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAlBA;;;;;;;;;;;;AA2Be,SAAS,cAAc,EACpC,UAAU,EACV,SAAS,EACT,SAAS,EACT,QAAQ,EACW;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;QACnE,MAAM;QACN,OAAO;QACP,SAAS;QACT,cAAc;QACd,mBAAmB;QACnB,4BAA4B;QAC5B,MAAM;QACN,gBAAgB;QAChB,oBAAoB;QACpB,iCAAiC;QACjC,cAAc;QACd,YAAY;QACZ,iBAAiB;IACnB;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,YAAY;gBACV,MAAM,UAAU,IAAI;gBACpB,OAAO,UAAU,KAAK;gBACtB,SAAS,UAAU,OAAO;gBAC1B,cAAc,UAAU,YAAY;gBACpC,mBAAmB,UAAU,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC5D,4BAA4B,UAAU,0BAA0B,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC9E,MAAM,UAAU,IAAI,IAAI;gBACxB,gBAAgB,UAAU,cAAc,IAAI;gBAC5C,oBAAoB,UAAU,kBAAkB,IAAI;gBACpD,iCAAiC,UAAU,+BAA+B,IAAI;gBAC9E,cAAc,UAAU,YAAY,IAAI;gBACxC,YAAY,UAAU,UAAU,IAAI;gBACpC,iBAAiB,UAAU,eAAe,IAAI;YAChD;QACF;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,OAAO,IAAI,GAAG;QAChB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO,OAAO,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,OAAO,iBAAiB,GAAG;QAC7B;QAEA,IAAI,CAAC,SAAS,0BAA0B,EAAE;YACxC,OAAO,0BAA0B,GAAG;QACtC;QAEA,IAAI,SAAS,iBAAiB,IAAI,SAAS,0BAA0B,EAAE;YACrE,MAAM,eAAe,IAAI,KAAK,SAAS,iBAAiB;YACxD,MAAM,WAAW,IAAI,KAAK,SAAS,0BAA0B;YAE7D,IAAI,YAAY,cAAc;gBAC5B,OAAO,0BAA0B,GAAG;YACtC;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,YAAY;YACZ,SAAS;YAET,IAAI,UAAU,WAAW;gBACvB,MAAM,iHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,YAAY,UAAU,YAAY,EAAE;YACzE,OAAO;gBACL,MAAM,iHAAA,CAAA,eAAY,CAAC,eAAe,CAAC,YAAY;YACjD;YAEA;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAyC;QAClE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,4CAA4C;QAC5C,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,2BAA2B,CAAC;QAChC,kBAAkB,qBAAqB;QAEvC,IAAI,SAAS,CAAC,SAAS,0BAA0B,EAAE;YACjD,MAAM,eAAe,IAAI,KAAK;YAC9B,MAAM,WAAW,IAAI,KAAK;YAC1B,SAAS,WAAW,CAAC,SAAS,WAAW,KAAK;YAC9C,kBAAkB,8BAA8B,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,SAAS,uBAAuB;;;;;;;sCAEnC,8OAAC;4BAAE,WAAU;sCACV,SAAS,8CAA8C;;;;;;;;;;;;;;;;;0BAK9D,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,WAAW,iBAAiB,IAAI,GAAG,mBAAmB;oDACtD,aAAY;;;;;;gDAEb,iBAAiB,IAAI,kBACpB,8OAAC;oDAAE,WAAU;8DAAwB,iBAAiB,IAAI;;;;;;;;;;;;sDAI9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,cAAc;oDAC9B,eAAe,CAAC,QAAU,kBAAkB,kBAAkB;;sEAE9D,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAgB;;;;;;8EAClC,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAW,iBAAiB,KAAK,GAAG,mBAAmB;oDACvD,aAAY;;;;;;gDAEb,iBAAiB,KAAK,kBACrB,8OAAC;oDAAE,WAAU;8DAAwB,iBAAiB,KAAK;;;;;;;;;;;;sDAI/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAW,iBAAiB,OAAO,GAAG,mBAAmB;oDACzD,aAAY;;;;;;gDAEb,iBAAiB,OAAO,kBACvB,8OAAC;oDAAE,WAAU;8DAAwB,iBAAiB,OAAO;;;;;;;;;;;;sDAIjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,YAAY;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDACjE,WAAW,iBAAiB,YAAY,GAAG,mBAAmB;oDAC9D,aAAY;;;;;;gDAEb,iBAAiB,YAAY,kBAC5B,8OAAC;oDAAE,WAAU;8DAAwB,iBAAiB,YAAY;;;;;;;;;;;;sDAItE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,eAAe;oDAC/B,eAAe,CAAC,QAAU,kBAAkB,mBAAmB;;sEAE/D,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;8EAC3B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAU;;;;;;8EAC5B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;oDACxD,WAAW,iBAAiB,iBAAiB,GAAG,mBAAmB;;;;;;gDAEpE,iBAAiB,iBAAiB,kBACjC,8OAAC;oDAAE,WAAU;8DAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;sDAI3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA6B;;;;;;8DAC5C,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,0BAA0B;oDAC1C,UAAU,CAAC,IAAM,kBAAkB,8BAA8B,EAAE,MAAM,CAAC,KAAK;oDAC/E,WAAW,iBAAiB,0BAA0B,GAAG,mBAAmB;;;;;;gDAE7E,iBAAiB,0BAA0B,kBAC1C,8OAAC;oDAAE,WAAU;8DAAwB,iBAAiB,0BAA0B;;;;;;;;;;;;sDAIpF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAqB;;;;;;8DACpC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;oDACvE,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkC;;;;;;8DACjD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,+BAA+B;oDAC/C,UAAU,CAAC,IAAM,kBAAkB,mCAAmC,EAAE,MAAM,CAAC,KAAK;oDACpF,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAe;;;;;;kEAC9B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDACjE,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;gDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;oBAMb,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAAgB;;;;;;;;;;;;kCAKhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAU,UAAU;;kDACrD,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGhC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;;oCACtC,yBAAW,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAiC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAC/E,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { FileText, Save, Loader2, AlertCircle, X, Settings, Plus } from 'lucide-react'\nimport { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'\nimport { useCertificazioneForm } from '@/hooks/useCertificazioneForm'\nimport StrumentoForm from './StrumentoForm'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  preselectedCavoId?: string\n  onSuccess: (certificazione: CertificazioneCavo) => void\n  onCancel: () => void\n  onStrumentiUpdate?: () => void\n}\n\n/**\n * Form di certificazione CEI 64-8 completamente ridisegnato\n * Layout moderno a singola colonna con sezioni organizzate\n */\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  preselectedCavoId,\n  onSuccess,\n  onCancel,\n  onStrumentiUpdate\n}: CertificazioneFormProps) {\n  // Stato per il modal del nuovo strumento\n  const [showStrumentoForm, setShowStrumentoForm] = useState(false)\n  const {\n    // Dati\n    formData,\n    cavi,\n    responsabili,\n    strumenti: strumentiFromHook,\n    weatherData,\n    selectedCavo,\n\n    // Stati\n    isLoading,\n    isSaving,\n    isLoadingWeather,\n    error,\n    validationErrors,\n    isWeatherOverride,\n    isEdit,\n    isCavoLocked,\n\n    // Funzioni\n    handleInputChange,\n    handleSubmit,\n    setIsWeatherOverride,\n    refreshStrumenti,\n    onCancel: handleCancel\n  } = useCertificazioneForm({\n    cantiereId,\n    certificazione,\n    strumenti,\n    preselectedCavoId,\n    onSuccess,\n    onCancel\n  })\n\n  // Usa gli strumenti dal hook se disponibili, altrimenti quelli passati come prop\n  const strumentiToUse = strumentiFromHook.length > 0 ? strumentiFromHook : strumenti\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Caricamento dati certificazione...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Render del componente principale\n  return (\n    <div className=\"min-w-[800px] max-w-4xl mx-auto bg-white\">\n      {/* Header Compatto */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <div className=\"flex items-center gap-2\">\n          <div className=\"p-1.5 bg-blue-100 rounded-lg\">\n            <FileText className=\"h-4 w-4 text-blue-600\" />\n          </div>\n          <div>\n            <h1 className=\"text-lg font-semibold text-gray-900\">\n              {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}\n            </h1>\n            {preselectedCavoId && (\n              <p className=\"text-xs text-gray-500\">Cavo: {preselectedCavoId}</p>\n            )}\n          </div>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={onCancel}\n          className=\"text-gray-400 hover:text-gray-600 h-8 w-8 p-0\"\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      {/* Alert Errori */}\n      {error && (\n        <div className=\"p-4 border-b border-red-200 bg-red-50\">\n          <Alert variant=\"destructive\" className=\"border-red-200 bg-red-50 py-2\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription className=\"text-sm\">{error}</AlertDescription>\n          </Alert>\n        </div>\n      )}\n\n      {/* Form Compatto */}\n      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className=\"p-6 space-y-6\">\n\n        {/* Sezione 1: Informazioni Base */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center gap-2 pb-2 border-b border-gray-100\">\n            <FileText className=\"h-4 w-4 text-blue-600\" />\n            <h2 className=\"text-base font-semibold text-gray-900\">Informazioni Base</h2>\n            <span className=\"text-xs text-gray-500\">Dati principali del cavo e operatore</span>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Informazioni Cavo Selezionato */}\n            {selectedCavo && (\n              <div className=\"md:col-span-2 p-4 bg-gray-50 rounded-lg border\">\n                <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Cavo: {selectedCavo.id_cavo}</h3>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm\">\n                  <div><span className=\"font-medium text-gray-600\">Tipologia:</span> <span className=\"font-bold\">{selectedCavo.tipologia || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Sezione:</span> <span className=\"font-bold\">{selectedCavo.sezione || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Sistema:</span> <span className=\"font-bold\">{selectedCavo.sistema || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Utility:</span> <span className=\"font-bold\">{selectedCavo.utility || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Da:</span> <span className=\"font-bold\">{selectedCavo.ubicazione_partenza || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">A:</span> <span className=\"font-bold\">{selectedCavo.ubicazione_arrivo || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Metri Teorici:</span> <span className=\"font-bold\">{selectedCavo.metri_teorici || 0}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Metri Reali:</span> <span className=\"font-bold\">{selectedCavo.metratura_reale || 0}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Stato:</span> <span className=\"font-bold\">{selectedCavo.stato_installazione || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Bobina:</span> <span className=\"font-bold\">{selectedCavo.id_bobina || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Colore:</span> <span className=\"font-bold\">{selectedCavo.colore_cavo || 'N/A'}</span></div>\n                  <div><span className=\"font-medium text-gray-600\">Collegamenti:</span> <span className=\"font-bold\">{selectedCavo.collegamenti || 0}</span></div>\n                </div>\n              </div>\n            )}\n\n            {/* Operatore */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"id_operatore\" className=\"text-xs font-medium text-gray-700\">Operatore</Label>\n              <Select\n                value={formData.id_operatore?.toString() || ''}\n                onValueChange={(value) => handleInputChange('id_operatore', parseInt(value))}\n              >\n                <SelectTrigger className=\"h-9 text-sm border-gray-300\">\n                  <SelectValue placeholder=\"Seleziona operatore...\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {responsabili.map((resp) => (\n                    <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                      <span className=\"text-sm\">{resp.nome_responsabile}</span>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Strumento */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"id_strumento\" className=\"text-xs font-medium text-gray-700\">Strumento di Misura</Label>\n              <div className=\"flex gap-2\">\n                <Select\n                  value={formData.id_strumento?.toString() || ''}\n                  onValueChange={(value) => {\n                    const strumento = strumentiToUse.find(s => s.id_strumento === parseInt(value))\n                    handleInputChange('id_strumento', parseInt(value))\n                    if (strumento) {\n                      handleInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n                    }\n                  }}\n                >\n                  <SelectTrigger className=\"h-9 text-sm border-gray-300 flex-1\">\n                    <SelectValue placeholder=\"Seleziona strumento...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {strumentiToUse.map((strumento) => (\n                      <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                        <span className=\"font-medium text-sm\">{strumento.nome}</span>\n                        <span className=\"text-xs text-gray-500 ml-2\">{strumento.marca} {strumento.modello}</span>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"h-9 w-9 p-0 shrink-0\"\n                  onClick={() => setShowStrumentoForm(true)}\n                  title=\"Aggiungi nuovo strumento\"\n                >\n                  <Plus className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n\n\n          </div>\n        </div>\n\n        {/* Sezione 2: Condizioni Ambientali */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center gap-2 pb-2 border-b border-gray-100\">\n            <span className=\"text-lg\">🌤️</span>\n            <h2 className=\"text-base font-semibold text-gray-900\">Condizioni Ambientali</h2>\n            <span className=\"text-xs text-gray-500\">Temperatura e umidità durante la certificazione</span>\n          </div>\n\n          {/* Dati Meteorologici Compatti */}\n          {weatherData && (\n            <div className={`p-3 rounded-lg border ${\n              weatherData.isDemo\n                ? 'bg-amber-50 border-amber-200'\n                : 'bg-emerald-50 border-emerald-200'\n            }`}>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  {isLoadingWeather ? (\n                    <Loader2 className=\"h-4 w-4 animate-spin text-blue-600\" />\n                  ) : (\n                    <span className=\"text-lg\">{weatherData.isDemo ? '🔧' : '🌤️'}</span>\n                  )}\n                  <div>\n                    <div className=\"text-sm font-semibold text-gray-900\">\n                      {weatherData.temperature}°C • {weatherData.humidity}% UR\n                    </div>\n                    {weatherData.city && (\n                      <div className=\"text-xs text-gray-600\">{weatherData.city}</div>\n                    )}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    📡 {weatherData.source}\n                  </div>\n                </div>\n\n                <Button\n                  type=\"button\"\n                  variant={isWeatherOverride ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setIsWeatherOverride(!isWeatherOverride)}\n                  className=\"h-7 text-xs\"\n                >\n                  {isWeatherOverride ? (\n                    <>\n                      <X className=\"h-3 w-3 mr-1\" />\n                      Automatico\n                    </>\n                  ) : (\n                    <>\n                      <Settings className=\"h-3 w-3 mr-1\" />\n                      Manuale\n                    </>\n                  )}\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {/* Override Manuale Compatto */}\n          {isWeatherOverride && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"temperatura_prova\" className=\"text-xs font-medium text-gray-700\">\n                  Temperatura (°C)\n                </Label>\n                <Input\n                  id=\"temperatura_prova\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formData.temperatura_prova || ''}\n                  onChange={(e) => handleInputChange('temperatura_prova', parseFloat(e.target.value))}\n                  placeholder=\"20.0\"\n                  className=\"h-8 text-sm\"\n                />\n              </div>\n\n              <div className=\"space-y-1\">\n                <Label htmlFor=\"umidita_prova\" className=\"text-xs font-medium text-gray-700\">\n                  Umidità Relativa (%)\n                </Label>\n                <Input\n                  id=\"umidita_prova\"\n                  type=\"number\"\n                  min=\"0\"\n                  max=\"100\"\n                  value={formData.umidita_prova || ''}\n                  onChange={(e) => handleInputChange('umidita_prova', parseFloat(e.target.value))}\n                  placeholder=\"50\"\n                  className=\"h-8 text-sm\"\n                />\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Sezione 3: Misurazioni e Test Compatte */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center gap-2 pb-2 border-b border-gray-100\">\n            <span className=\"text-lg\">⚡</span>\n            <h2 className=\"text-base font-semibold text-gray-900\">Misurazioni e Test CEI 64-8</h2>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n            {/* Continuità */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"valore_continuita\" className=\"text-xs font-medium text-gray-700\">\n                Continuità\n              </Label>\n              <Select\n                value={formData.valore_continuita || 'OK'}\n                onValueChange={(value) => handleInputChange('valore_continuita', value)}\n              >\n                <SelectTrigger className=\"h-8 text-sm\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"OK\">✅ OK</SelectItem>\n                  <SelectItem value=\"NON_OK\">❌ NON OK</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Isolamento */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"valore_isolamento\" className=\"text-xs font-medium text-gray-700\">\n                Isolamento (MΩ)\n              </Label>\n              <Input\n                id=\"valore_isolamento\"\n                value={formData.valore_isolamento || ''}\n                onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}\n                placeholder=\"500\"\n                className=\"h-8 text-sm\"\n              />\n            </div>\n\n            {/* Resistenza */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"valore_resistenza\" className=\"text-xs font-medium text-gray-700\">\n                Resistenza (Ω)\n              </Label>\n              <Input\n                id=\"valore_resistenza\"\n                value={formData.valore_resistenza || ''}\n                onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}\n                placeholder=\"0.5\"\n                className=\"h-8 text-sm\"\n              />\n            </div>\n\n\n\n            {/* Tensione Prova */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"tensione_prova_isolamento\" className=\"text-xs font-medium text-gray-700\">\n                Tensione Prova (V)\n              </Label>\n              <Input\n                id=\"tensione_prova_isolamento\"\n                type=\"number\"\n                value={formData.tensione_prova_isolamento || ''}\n                onChange={(e) => handleInputChange('tensione_prova_isolamento', parseInt(e.target.value))}\n                placeholder=\"500\"\n                className=\"h-8 text-sm\"\n              />\n            </div>\n\n            {/* Esito */}\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"esito_complessivo\" className=\"text-xs font-medium text-gray-700\">\n                Esito Complessivo\n              </Label>\n              <Select\n                value={formData.esito_complessivo || 'CONFORME'}\n                onValueChange={(value) => handleInputChange('esito_complessivo', value)}\n              >\n                <SelectTrigger className=\"h-8 text-sm\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"CONFORME\">✅ CONFORME</SelectItem>\n                  <SelectItem value=\"NON_CONFORME\">❌ NON CONFORME</SelectItem>\n                  <SelectItem value=\"CONFORME_CON_OSSERVAZIONI\">⚠️ CONFORME CON OSSERVAZIONI</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </div>\n\n        {/* Note Compatte */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center gap-2 pb-2 border-b border-gray-100\">\n            <span className=\"text-lg\">📝</span>\n            <h2 className=\"text-base font-semibold text-gray-900\">Note</h2>\n          </div>\n\n          <div className=\"space-y-1\">\n            <Label htmlFor=\"note\" className=\"text-xs font-medium text-gray-700\">\n              Osservazioni aggiuntive\n            </Label>\n            <Textarea\n              id=\"note\"\n              value={formData.note || ''}\n              onChange={(e) => handleInputChange('note', e.target.value)}\n              placeholder=\"Inserisci eventuali note o osservazioni...\"\n              className=\"h-16 text-sm resize-none\"\n            />\n          </div>\n        </div>\n\n        {/* Footer Azioni */}\n        <div className=\"flex justify-end gap-3 pt-4 border-t border-gray-200\">\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={onCancel}\n            disabled={isSaving}\n            className=\"px-4 h-9\"\n          >\n            Annulla\n          </Button>\n          <Button\n            type=\"submit\"\n            disabled={isSaving}\n            className=\"px-6 h-9 bg-green-600 hover:bg-green-700\"\n          >\n            {isSaving ? (\n              <>\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                Salvando...\n              </>\n            ) : (\n              <>\n                <Save className=\"h-4 w-4 mr-2\" />\n                Salva e Chiudi\n              </>\n            )}\n          </Button>\n        </div>\n      </form>\n\n      {/* Modal Nuovo Strumento */}\n      <Dialog open={showStrumentoForm} onOpenChange={setShowStrumentoForm}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle>Aggiungi Nuovo Strumento</DialogTitle>\n          </DialogHeader>\n          <StrumentoForm\n            cantiereId={cantiereId}\n            onSuccess={() => {\n              setShowStrumentoForm(false)\n              // Ricarica la lista degli strumenti\n              refreshStrumenti()\n              if (onStrumentiUpdate) {\n                onStrumentiUpdate()\n              }\n            }}\n            onCancel={() => setShowStrumentoForm(false)}\n          />\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAxBA;;;;;;;;;;;;;AAwCe,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACR,iBAAiB,EACO;IACxB,yCAAyC;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EACJ,OAAO;IACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,WAAW,iBAAiB,EAC5B,WAAW,EACX,YAAY,EAEZ,QAAQ;IACR,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,KAAK,EACL,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,EACN,YAAY,EAEZ,WAAW;IACX,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,gBAAgB,EAChB,UAAU,YAAY,EACvB,GAAG,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,iFAAiF;IACjF,MAAM,iBAAiB,kBAAkB,MAAM,GAAG,IAAI,oBAAoB;IAE1E,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mCAAmC;IACnC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,SAAS,4BAA4B;;;;;;oCAEvC,mCACC,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAO;;;;;;;;;;;;;;;;;;;kCAIlD,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKhB,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;;sCACrC,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAW;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAK,UAAU,CAAC;oBAAQ,EAAE,cAAc;oBAAI;gBAAgB;gBAAG,WAAU;;kCAGxE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;oCAEZ,8BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDAAyC;oDAAO,aAAa,OAAO;;;;;;;0DAClF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAiB;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,SAAS,IAAI;;;;;;;;;;;;kEAC1H,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAe;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,OAAO,IAAI;;;;;;;;;;;;kEACtH,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAe;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,OAAO,IAAI;;;;;;;;;;;;kEACtH,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAe;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,OAAO,IAAI;;;;;;;;;;;;kEACtH,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAU;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,mBAAmB,IAAI;;;;;;;;;;;;kEAC7H,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAS;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,iBAAiB,IAAI;;;;;;;;;;;;kEAC1H,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAqB;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,aAAa,IAAI;;;;;;;;;;;;kEAClI,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAmB;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,eAAe,IAAI;;;;;;;;;;;;kEAClI,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAa;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,mBAAmB,IAAI;;;;;;;;;;;;kEAChI,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAc;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,SAAS,IAAI;;;;;;;;;;;;kEACvH,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAc;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,WAAW,IAAI;;;;;;;;;;;;kEACzH,8OAAC;;0EAAI,8OAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAoB;0EAAC,8OAAC;gEAAK,WAAU;0EAAa,aAAa,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kDAMtI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;0DAAoC;;;;;;0DAC5E,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,YAAY,EAAE,cAAc;gDAC5C,eAAe,CAAC,QAAU,kBAAkB,gBAAgB,SAAS;;kEAErE,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;gEAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;0EACzE,cAAA,8OAAC;oEAAK,WAAU;8EAAW,KAAK,iBAAiB;;;;;;+DADlC,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;kDAS7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;0DAAoC;;;;;;0DAC5E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,YAAY,EAAE,cAAc;wDAC5C,eAAe,CAAC;4DACd,MAAM,YAAY,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;4DACvE,kBAAkB,gBAAgB,SAAS;4DAC3C,IAAI,WAAW;gEACb,kBAAkB,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;4DACrF;wDACF;;0EAEA,8OAAC,kIAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,eAAe,GAAG,CAAC,CAAC,0BACnB,8OAAC,kIAAA,CAAA,aAAU;wEAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;;0FAC7E,8OAAC;gFAAK,WAAU;0FAAuB,UAAU,IAAI;;;;;;0FACrD,8OAAC;gFAAK,WAAU;;oFAA8B,UAAU,KAAK;oFAAC;oFAAE,UAAU,OAAO;;;;;;;;uEAFlE,UAAU,YAAY;;;;;;;;;;;;;;;;kEAO7C,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,qBAAqB;wDACpC,OAAM;kEAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;4BAIzC,6BACC,8OAAC;gCAAI,WAAW,CAAC,sBAAsB,EACrC,YAAY,MAAM,GACd,iCACA,oCACJ;0CACA,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,iCACC,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAEnB,8OAAC;oDAAK,WAAU;8DAAW,YAAY,MAAM,GAAG,OAAO;;;;;;8DAEzD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;gEACZ,YAAY,WAAW;gEAAC;gEAAM,YAAY,QAAQ;gEAAC;;;;;;;wDAErD,YAAY,IAAI,kBACf,8OAAC;4DAAI,WAAU;sEAAyB,YAAY,IAAI;;;;;;;;;;;;8DAG5D,8OAAC;oDAAI,WAAU;;wDAAwB;wDACjC,YAAY,MAAM;;;;;;;;;;;;;sDAI1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,oBAAoB,YAAY;4CACzC,MAAK;4CACL,SAAS,IAAM,qBAAqB,CAAC;4CACrC,WAAU;sDAET,kCACC;;kEACE,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;6EAIhC;;kEACE,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAUhD,mCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAoB,WAAU;0DAAoC;;;;;;0DAGjF,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,iBAAiB,IAAI;gDACrC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;gDACjF,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;0DAAoC;;;;;;0DAG7E,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,SAAS,aAAa,IAAI;gDACjC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC7E,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;;0CAGxD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAoB,WAAU;0DAAoC;;;;;;0DAGjF,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,iBAAiB,IAAI;gDACrC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;kEAEjE,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAK;;;;;;0EACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAMjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAoB,WAAU;0DAAoC;;;;;;0DAGjF,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,iBAAiB,IAAI;gDACrC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gDACtE,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAoB,WAAU;0DAAoC;;;;;;0DAGjF,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,iBAAiB,IAAI;gDACrC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gDACtE,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAOd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAA4B,WAAU;0DAAoC;;;;;;0DAGzF,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,yBAAyB,IAAI;gDAC7C,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;gDACvF,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAoB,WAAU;0DAAoC;;;;;;0DAGjF,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,iBAAiB,IAAI;gDACrC,eAAe,CAAC,QAAU,kBAAkB,qBAAqB;;kEAEjE,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;0EACjC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;;;;;;;0CAGxD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAO,WAAU;kDAAoC;;;;;;kDAGpE,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,yBACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;0BAS3C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAmB,cAAc;0BAC7C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC,qJAAA,CAAA,UAAa;4BACZ,YAAY;4BACZ,WAAW;gCACT,qBAAqB;gCACrB,oCAAoC;gCACpC;gCACA,IAAI,mBAAmB;oCACrB;gCACF;4BACF;4BACA,UAAU,IAAM,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}]}