import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    if (!body.codice_univoco || !body.password) {
      return NextResponse.json(
        { 
          detail: 'Codice univoco e password sono richiesti' 
        }, 
        { status: 400 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    console.log('🔄 Auth API: Proxying cantiere login request to backend:', `${backendUrl}/api/auth/login/cantiere`)
    
    const response = await fetch(`${backendUrl}/api/auth/login/cantiere`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    })

    console.log('📡 Auth API: Backend response status:', response.status)

    const data = await response.json()
    console.log('📡 Auth API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Auth API: Cantiere login error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
